/* eslint-disable max-lines-per-function */
'use client';
import { Icons } from '@assets';
import { connection, Polyton } from '@constants';
import { useBottomSheetStore } from '@entities/bottom-sheet';
import { useSelectedWallet } from '@entities/wallet/hooks';
import { SelectWallet } from '@features/select-wallet';
import { useQuery } from '@tanstack/react-query';
import { App, Token } from '@types';
import { Button, SwapForm } from '@ui';
import React, { FC, useCallback, useMemo, useState } from 'react';

import {
    useBalances,
    useTokenInit,
    useTrade,
    useTradeBondingCurve,
    useEthereumAmountOut,
} from '../hooks';
import styles from './swap.module.scss';

interface ISwapProps {
    tokenFrom?: Token;
    setTokenFrom: (token: Token) => void;
    tokenTo?: Token;
    setTokenTo: (token: Token) => void;
    app: App;
    isLoading?: boolean;
    assets?: Token[] | undefined;
    customColor?: string;
}

export const Swap: FC<ISwapProps> = ({
    tokenFrom,
    setTokenFrom,
    tokenTo,
    setTokenTo,
    isLoading,
    customColor,
    app,
}) => {
    const walletConnection = useSelectedWallet();
    const wallet = walletConnection?.wallet;
    const [amount, setAmount] = useState('');

    const polytonContract = useMemo(() => {
        if (!connection) {
            return null;
        }

        return new Polyton(connection, { verboseLog: false });
    }, []);

    const project = useMemo(() => {
        if (
            !polytonContract ||
            !app.token!.contractAddress ||
            app.token?.chain !== 'solana'
        ) {
            return null;
        }

        return polytonContract.project(app.token!.contractAddress);
    }, [polytonContract, app.token]);

    const { data: isProjectExist } = useQuery({
        queryKey: ['projectExist', app.token!.contractAddress],
        queryFn: async () => {
            // if (!project) {
            //     return false;
            // }

            return true;
        },
    });

    const { initToken, isLoading: isTokenInitLoading } = useTokenInit({
        contract: polytonContract,
        appId: app.id,
    });

    const { tokenFromBalance, tokenToBalance } = useBalances({
        tokenFromType: tokenFrom?.type,
        project,
    });

    const tradeType = useMemo(
        () => (tokenFrom?.type === 'native' ? 'buy' : 'sell'),
        [tokenFrom],
    );

    const {
        trade,
        amount: estimateSwapOut,
        isLoading: isEstimateLoading,
    } = useTrade({
        project,
        amount,
        type: tradeType,
    });

    // Use Ethereum contract for amount calculation when token is on Ethereum
    const {
        amountOut: ethereumAmountOut,
        netAmountOut: ethereumNetAmountOut,
        isLoading: isEthereumEstimateLoading,
    } = useEthereumAmountOut({
        contractAddress: app.token?.contractAddress || null,
        amount,
        tradeType,
        enabled: app.token?.chain !== 'solana' && !!app.token?.contractAddress,
    });

    const { bondingCurveTrade } = useTradeBondingCurve({
        project,
        trade,
        amount,
        type: tradeType,
        appId: app.id,
    });

    // Determine which amount and loading state to use based on chain
    const isEthereumToken =
        app.token?.chain !== 'solana' && !!app.token?.contractAddress;
    const finalAmountOut = isEthereumToken
        ? tradeType === 'sell'
            ? ethereumNetAmountOut
            : ethereumAmountOut
        : estimateSwapOut?.toString();
    const finalIsLoading = isEthereumToken
        ? isEthereumEstimateLoading
        : isEstimateLoading;

    const handleSwitchTokens = useCallback(() => {
        if (tokenFrom && tokenTo) {
            setTokenFrom(tokenTo);
            setTokenTo(tokenFrom);
        }
    }, [tokenFrom, tokenTo, setTokenFrom, setTokenTo]);

    const { show, close } = useBottomSheetStore();

    const connectClickHandler = useCallback(() => {
        show({
            title: 'Wallet',
            customContent: <SelectWallet onSelect={close} />,
        });
    }, [show, close]);

    const swapClickHandler = useCallback(async () => {
        await bondingCurveTrade();
    }, [bondingCurveTrade]);

    const isError = useMemo(
        () => Number(amount) > Number(tokenFromBalance),
        [amount, tokenFromBalance],
    );

    const buttonContent = useMemo(() => {
        if (isTokenInitLoading) {
            return (
                <Button
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                    disabled
                >
                    Initializing <Icons.Loader style={{ marginLeft: 8 }} />
                </Button>
            );
        }

        if (!wallet) {
            return (
                <Button
                    onClick={connectClickHandler}
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Connect Wallet
                </Button>
            );
        }

        if (!isProjectExist) {
            return (
                <Button
                    onClick={initToken}
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                >
                    Initialize the token
                </Button>
            );
        }

        if (isError) {
            return (
                <Button
                    disabled
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Insufficient balance
                </Button>
            );
        }

        if (Number(amount) < 0.0001) {
            return (
                <Button
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                    disabled
                >
                    Minimum amount is 0.01
                </Button>
            );
        }

        if (amount === '0') {
            return (
                <Button
                    disabled
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Enter the amount
                </Button>
            );
        }

        return (
            <Button
                onClick={swapClickHandler}
                view={tradeType === 'buy' ? 'green' : 'danger'}
                size="xl"
                borderRadius="xxl"
                full
            >
                {tradeType === 'buy' ? 'Buy' : 'Sell'}
            </Button>
        );
    }, [
        swapClickHandler,
        initToken,
        connectClickHandler,
        tradeType,
        wallet,
        customColor,
        isError,
        amount,
        isProjectExist,
        isTokenInitLoading,
    ]);

    return (
        <div className={styles.container}>
            <SwapForm
                fromAmount={amount}
                setFromAmount={setAmount}
                isEstimateLoading={finalIsLoading}
                toAmount={finalAmountOut || ''}
                tokenTo={tokenTo}
                tokenFrom={tokenFrom}
                onSwitchTokens={handleSwitchTokens}
                balanceFrom={tokenFromBalance.toString()}
                balanceTo={tokenToBalance.toString()}
                isLoading={isLoading}
                customColor={customColor}
            />
            {buttonContent}
        </div>
    );
};
