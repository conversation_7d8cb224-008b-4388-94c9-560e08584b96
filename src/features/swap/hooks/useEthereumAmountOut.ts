import { useState, useEffect } from 'react';
import { usePublicClient, useReadContract } from 'wagmi';
import { parseEther, formatUnits, Address } from 'viem';
import { SODA_FACTORY_ADDRESS, ETHEREUM_NATIVE_ADDRESS } from '@shared/constants';
import SodaFactoryABI from '@shared/abi/SodaFactoryABI.json';

interface UseEthereumAmountOutProps {
    tokenFromAddress?: Address;
    tokenToAddress?: Address;
    amount: string;
    isEnabled?: boolean;
}

export const useEthereumAmountOut = ({
    tokenFromAddress,
    tokenToAddress,
    amount,
    isEnabled = true,
}: UseEthereumAmountOutProps) => {
    const [estimatedAmountOut, setEstimatedAmountOut] = useState<string>('0');
    const [isLoading, setIsLoading] = useState(false);
    const publicClient = usePublicClient();

    const isBuyOperation = tokenFromAddress === ETHEREUM_NATIVE_ADDRESS || !tokenFromAddress;

    // Get virtual reserves for calculation
    const { data: virtualTokenReserves } = useReadContract({
        address: SODA_FACTORY_ADDRESS,
        abi: SodaFactoryABI.abi,
        functionName: 'virtualTokenReserves',
        query: { enabled: isEnabled },
    });

    const { data: virtualCollateralReserves } = useReadContract({
        address: SODA_FACTORY_ADDRESS,
        abi: SodaFactoryABI.abi,
        functionName: 'virtualCollateralReserves',
        query: { enabled: isEnabled },
    });

    const { data: feeBasisPoints } = useReadContract({
        address: SODA_FACTORY_ADDRESS,
        abi: SodaFactoryABI.abi,
        functionName: 'feeBasisPoints',
        query: { enabled: isEnabled },
    });

    useEffect(() => {
        const calculateAmountOut = async () => {
            if (
                !amount || 
                amount === '0' || 
                !virtualTokenReserves ||
                !virtualCollateralReserves ||
                !feeBasisPoints ||
                !isEnabled
            ) {
                setEstimatedAmountOut('0');
                return;
            }

            setIsLoading(true);
            try {
                // Parse the input amount
                const amountIn = parseEther(amount);
                
                // Calculate fee
                const fee = (amountIn * BigInt(feeBasisPoints.toString())) / BigInt(10000);
                const amountInAfterFee = amountIn - fee;

                let amountOut: bigint;

                if (isBuyOperation) {
                    // Buy operation: ETH -> Token
                    // Using constant product formula: x * y = k
                    // amountOut = (amountIn * virtualTokenReserves) / (virtualCollateralReserves + amountIn)
                    const virtualCollateralReservesBI = BigInt(virtualCollateralReserves.toString());
                    const virtualTokenReservesBI = BigInt(virtualTokenReserves.toString());
                    
                    amountOut = (amountInAfterFee * virtualTokenReservesBI) / 
                               (virtualCollateralReservesBI + amountInAfterFee);
                } else {
                    // Sell operation: Token -> ETH
                    // amountOut = (amountIn * virtualCollateralReserves) / (virtualTokenReserves + amountIn)
                    const virtualCollateralReservesBI = BigInt(virtualCollateralReserves.toString());
                    const virtualTokenReservesBI = BigInt(virtualTokenReserves.toString());
                    
                    amountOut = (amountInAfterFee * virtualCollateralReservesBI) / 
                               (virtualTokenReservesBI + amountInAfterFee);
                }

                // Format the result
                const formattedAmountOut = formatUnits(amountOut, 18);
                setEstimatedAmountOut(formattedAmountOut);
            } catch (error) {
                console.error('Error calculating amount out:', error);
                setEstimatedAmountOut('0');
            } finally {
                setIsLoading(false);
            }
        };

        calculateAmountOut();
    }, [
        amount,
        virtualTokenReserves,
        virtualCollateralReserves,
        feeBasisPoints,
        isBuyOperation,
        isEnabled,
    ]);

    return {
        estimatedAmountOut,
        isLoading,
    };
};
