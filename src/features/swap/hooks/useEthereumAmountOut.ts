'use client';
import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { createPublicClient, http, parseEther, formatEther } from 'viem';
import { mainnet, sepolia } from 'viem/chains';
import { define<PERSON>hain } from 'viem';
import { useDeferredValue } from '@hooks';
import SodaTokenABI from '@shared/abi/SodaTokenABI.json';

// Define local development chain (same as in wagmi config)
const localChain = defineChain({
    id: 1337,
    name: 'Local Development',
    nativeCurrency: {
        decimals: 18,
        name: 'Ether',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: [process.env.NEXT_PUBLIC_RPC_URL || 'http://localhost:8545'],
        },
    },
    blockExplorers: {
        default: { name: 'Explorer', url: '' },
    },
});

// Get the appropriate chain based on environment
const getChain = () => {
    const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;
    
    switch (chainId) {
        case '1':
            return mainnet;
        case '11155111':
            return sepolia;
        case '1337':
        default:
            return localChain;
    }
};

// Create public client for reading contract data
const publicClient = createPublicClient({
    chain: getChain(),
    transport: http(
        getChain().id === localChain.id 
            ? process.env.NEXT_PUBLIC_RPC_URL || 'http://localhost:8545'
            : undefined
    ),
});

interface UseEthereumAmountOutParams {
    contractAddress: string | null;
    amount: string;
    tradeType: 'buy' | 'sell';
    enabled?: boolean;
}

interface AmountOutResult {
    amountOut: string | null;
    fee: string | null;
    netAmountOut: string | null; // For sell trades, this is amountOut - fee
    isLoading: boolean;
    error: Error | null;
}

export const useEthereumAmountOut = ({
    contractAddress,
    amount,
    tradeType,
    enabled = true,
}: UseEthereumAmountOutParams): AmountOutResult => {
    const debouncedAmount = useDeferredValue(amount, 500);

    const { data, isLoading, error } = useQuery({
        queryKey: [
            'ethereumAmountOut',
            contractAddress,
            debouncedAmount,
            tradeType,
        ],
        queryFn: async () => {
            if (!contractAddress || !debouncedAmount || Number(debouncedAmount) <= 0) {
                return null;
            }

            try {
                // Convert amount to wei (18 decimals for ETH)
                const amountInWei = parseEther(debouncedAmount);

                // Call getAmountOut function
                // For buy: _collateralTokenIsIn = true (ETH in, tokens out)
                // For sell: _collateralTokenIsIn = false (tokens in, ETH out)
                const result = await publicClient.readContract({
                    address: contractAddress as `0x${string}`,
                    abi: SodaTokenABI.abi,
                    functionName: 'getAmountOut',
                    args: [amountInWei, tradeType === 'buy'],
                });

                const [amountOut, fee] = result as [bigint, bigint];

                // Convert back to ether format for display
                const amountOutFormatted = formatEther(amountOut);
                const feeFormatted = formatEther(fee);

                // For sell trades, calculate net amount (amountOut - fee)
                let netAmountOut = amountOutFormatted;
                if (tradeType === 'sell') {
                    const netAmount = amountOut - fee;
                    netAmountOut = formatEther(netAmount);
                }

                return {
                    amountOut: amountOutFormatted,
                    fee: feeFormatted,
                    netAmountOut,
                };
            } catch (err) {
                console.error('Error calling getAmountOut:', err);
                throw err;
            }
        },
        enabled: enabled && !!contractAddress && Number(debouncedAmount) > 0,
        refetchInterval: 5_000, // Refetch every 5 seconds to keep data fresh
        retry: 3,
    });

    return useMemo(
        () => ({
            amountOut: data?.amountOut || null,
            fee: data?.fee || null,
            netAmountOut: data?.netAmountOut || null,
            isLoading,
            error: error as Error | null,
        }),
        [data, isLoading, error],
    );
};
