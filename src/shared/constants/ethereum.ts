import { Address } from 'viem';

// Contract addresses - configurable by environment
export const getSodaFactoryAddress = (): Address => {
    // Check for environment variable first
    if (process.env.NEXT_PUBLIC_SODA_FACTORY_ADDRESS) {
        return process.env.NEXT_PUBLIC_SODA_FACTORY_ADDRESS as Address;
    }

    // Default addresses based on chain
    const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;

    switch (chainId) {
        case '1': // Mainnet
            return '******************************************' as Address; // Replace with actual mainnet address
        case '11155111': // Sepolia
            return '******************************************' as Address; // Replace with actual sepolia address
        case '1337': // Local development
        default:
            return '******************************************' as Address; // Common local deployment address
    }
};

export const ETHEREUM_NATIVE_ADDRESS: Address =
    '******************************************';

// Standard ERC20 ABI - minimal functions needed for token operations
export const ERC20_ABI = [
    {
        name: 'balanceOf',
        type: 'function',
        stateMutability: 'view',
        inputs: [{ name: 'account', type: 'address' }],
        outputs: [{ name: '', type: 'uint256' }],
    },
    {
        name: 'allowance',
        type: 'function',
        stateMutability: 'view',
        inputs: [
            { name: 'owner', type: 'address' },
            { name: 'spender', type: 'address' },
        ],
        outputs: [{ name: '', type: 'uint256' }],
    },
    {
        name: 'approve',
        type: 'function',
        stateMutability: 'nonpayable',
        inputs: [
            { name: 'spender', type: 'address' },
            { name: 'amount', type: 'uint256' },
        ],
        outputs: [{ name: '', type: 'bool' }],
    },
    {
        name: 'decimals',
        type: 'function',
        stateMutability: 'view',
        inputs: [],
        outputs: [{ name: '', type: 'uint8' }],
    },
] as const;

// Maximum uint256 value for infinite approval
export const MAX_UINT256 =
    '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff' as const;
