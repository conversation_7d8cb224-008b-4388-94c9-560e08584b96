import { createPublicClient, formatEther, http, define<PERSON>hain } from 'viem';
import { mainnet, sepolia } from 'viem/chains';

// Define local development chain (same as in wagmi config)
const localChain = defineChain({
    id: 1337,
    name: 'Local Development',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON>ther',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: [process.env.NEXT_PUBLIC_RPC_URL || 'http://localhost:8545'],
        },
    },
    blockExplorers: {
        default: { name: 'Explorer', url: '' },
    },
});

// Determine which chain and transport to use
const getChainConfig = () => {
    const rpcUrl = process.env.NEXT_PUBLIC_RPC_URL;

    // If RPC_URL is set to localhost, use local chain
    if (rpcUrl && rpcUrl.includes('localhost')) {
        return {
            chain: localChain,
            transport: http(rpcUrl),
        };
    }

    // Otherwise use mainnet/sepolia based on NODE_ENV
    const chain = process.env.NODE_ENV === 'production' ? mainnet : sepolia;
    return {
        chain,
        transport: http(),
    };
};

const { chain, transport } = getChainConfig();

const publicClient = createPublicClient({
    chain,
    transport,
});

export const fetchBalance = async (address: string) => {
    if (!address) {
        return 0;
    }

    try {
        const balance = await publicClient.getBalance({
            address: address as `0x${string}`,
        });

        return parseFloat(formatEther(balance));
    } catch (error) {
        console.error('Error fetching balance:', error);
        return 0;
    }
};
